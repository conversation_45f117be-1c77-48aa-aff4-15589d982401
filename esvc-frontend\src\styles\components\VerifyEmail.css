/* Verify Email Container */
.verify-email-container {
  min-height: 100vh;
  background: #1A1A1A;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
}



/* Main Form */
.verify-email-form {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px;
  gap: 32px;
  position: absolute;
  width: 560px;
  height: auto;
  min-height: 480px;
  left: calc(50% - 560px/2);
  top: 120px;
  background: #262626;
  border-radius: 24px;
}

.resent-form {
  min-height: 420px;
}

.success-form {
  min-height: 320px;
  justify-content: center;
}

/* Form Content */
.form-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  width: 100%;
}

.success-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  text-align: center;
}

/* Email Icon */
.email-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.success-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

/* Form Header */
.form-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  text-align: center;
  max-width: 480px;
}

.form-title {
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  font-size: 24px;
  line-height: 32px;
  color: #FFFFFF;
  margin: 0;
}

.success-title {
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  font-size: 24px;
  line-height: 32px;
  color: #FFFFFF;
  margin: 0;
}

.form-subtitle {
  font-family: 'Montserrat', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: #CCCCCC;
  margin: 0;
}

.success-subtitle {
  font-family: 'Montserrat', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: #CCCCCC;
  margin: 0;
}

.form-message {
  font-family: 'Montserrat', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: #999999;
  margin: 0;
}

/* Verification Inputs */
.verification-inputs {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin: 16px 0;
}

.verification-input {
  width: 56px;
  height: 56px;
  background: #404040;
  border: 2px solid #525252;
  border-radius: 12px;
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  font-size: 24px;
  color: #FFFFFF;
  text-align: center;
  transition: all 0.3s ease;
  outline: none;
}

.verification-input:focus {
  border-color: #BF4129;
  background: #4A4A4A;
}

.verification-input.focused {
  border-color: #BF4129;
  background: #4A4A4A;
}

.verification-input.error {
  border-color: #FF6B6B;
  background: #4A2626;
}

/* Error Message */
.error-message {
  font-family: 'Montserrat', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: #FF6B6B;
  text-align: center;
  margin: 0;
}

/* Buttons */
.verify-button {
  width: 100%;
  height: 56px;
  background: #BF4129;
  border: none;
  border-radius: 12px;
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  font-size: 16px;
  color: #FFFFFF;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 8px;
}

.verify-button:hover:not(:disabled) {
  background: #A63622;
}

.verify-button:disabled {
  background: #666666;
  cursor: not-allowed;
}

.continue-button {
  width: 100%;
  height: 56px;
  background: #BF4129;
  border: none;
  border-radius: 12px;
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  font-size: 16px;
  color: #FFFFFF;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.continue-button:hover {
  background: #A63622;
}

/* Form Actions */
.form-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  width: 100%;
  margin-top: 16px;
}

.resend-button {
  background: none;
  border: none;
  font-family: 'Montserrat', sans-serif;
  font-weight: 500;
  font-size: 14px;
  color: #BF4129;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: underline;
}

.resend-button:hover:not(:disabled) {
  color: #A63622;
}

.resend-button:disabled {
  color: #666666;
  cursor: not-allowed;
  text-decoration: none;
}

.change-email-button {
  background: none;
  border: none;
  font-family: 'Montserrat', sans-serif;
  font-weight: 500;
  font-size: 14px;
  color: #D19049;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.change-email-button:hover {
  color: #B8793D;
}

.change-email-button span {
  font-size: 16px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .verify-email-container {
    padding: 16px;
  }

  .verify-email-form {
    width: calc(100vw - 32px);
    max-width: 400px;
    left: 50%;
    transform: translateX(-50%);
    padding: 24px;
    gap: 24px;
    min-height: 420px;
    top: 80px;
  }

  .success-form {
    min-height: 280px;
  }

  .resent-form {
    min-height: 380px;
  }

  .form-title,
  .success-title {
    font-size: 20px;
    line-height: 28px;
  }

  .form-subtitle,
  .success-subtitle {
    font-size: 14px;
    line-height: 20px;
  }

  .verification-inputs {
    gap: 8px;
  }

  .verification-input {
    width: 48px;
    height: 48px;
    font-size: 20px;
  }
}
