import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import '../styles/components/Overview.css';
import Header from './Header';
import Footer from './Footer';

// Import icons
import overviewIcon from '../assets/overview.png';
import walletMoneyIcon from '../assets/wallet-money.png';
import arrow2Icon from '../assets/arrow-2.png';
import cardCoinIcon from '../assets/card-coin.png';
import moneysIcon from '../assets/moneys.png';
import percentageSquareIcon from '../assets/percentage-square.png';
import chartIcon from '../assets/chart.png';
import spanImage from '../assets/span.png';
import trendUpIcon from '../assets/trend-up.png';
import informationCircleIcon from '../assets/information-circle.png';

interface OverviewProps {
  onNavigateToSignUp?: () => void;
  onNavigateToLogin?: () => void;
  onNavigateToLanding?: () => void;
}

const Overview: React.FC<OverviewProps> = ({ 
  onNavigateToSignUp, 
  onNavigateToLogin, 
  onNavigateToLanding 
}) => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('overview');

  const sidebarItems = [
    { id: 'overview', label: 'Overview', icon: overviewIcon, active: true },
    { id: 'live-reserve', label: 'Live Reserve', icon: walletMoneyIcon },
    { id: 'daily-transactions', label: 'Daily Transactions', icon: arrow2Icon },
    { id: 'real-time-staking', label: 'Real-Time Staking', icon: cardCoinIcon },
    { id: 'startup-funding', label: 'Startup Funding', icon: moneysIcon },
    { id: 'roi-distribution', label: 'ROI Distribution', icon: percentageSquareIcon },
    { id: 'visual-analytics', label: 'Visual Analytics', icon: chartIcon }
  ];

  const dashboardCards = [
    {
      title: 'TOTAL AMOUNT OF ESVC STAKED',
      value: '177,874,389.00',
      unit: 'ESVC',
      change: '+2.4% Today',
      changeType: 'positive',
      changeIcon: trendUpIcon
    },
    {
      title: 'FUNDS AVAILABLE FOR DAILY ISO PAYOUTS',
      value: '$609,185',
      change: '+7.8% Today',
      changeType: 'positive',
      changeIcon: trendUpIcon
    },
    {
      title: 'FUNDS AVAILABLE TO FUND STARTUPS',
      value: '$43,700',
      change: '+4.8% Today',
      changeType: 'positive',
      changeIcon: trendUpIcon,
      titleIcon: informationCircleIcon
    },
    {
      title: 'TOTAL NUMBER OF STAKERS',
      value: '11,302',
      change: '+3.8% Today',
      changeType: 'positive',
      changeIcon: trendUpIcon
    },
    {
      title: 'TOTAL ESVC SOLD TO DATE',
      value: '$29,400,200.00',
      change: '+2.4% Today',
      changeType: 'positive',
      changeIcon: trendUpIcon
    },
    {
      title: 'TOTAL PROFIT GENERATED',
      value: '$43,700',
      change: '+1.8% Today',
      changeType: 'positive',
      changeIcon: trendUpIcon
    }
  ];

  const handleSidebarClick = (itemId: string) => {
    setActiveTab(itemId);
  };

  return (
    <div className="overview-container">
      {/* Background Blur Gradients */}
      <div className="blur-gradient blur-gradient-1"></div>
      <div className="blur-gradient blur-gradient-2"></div>

      {/* Header */}
      <Header
        onNavigateToSignUp={() => navigate('/signup')}
        onNavigateToLogin={() => navigate('/login')}
        onNavigateToLanding={() => navigate('/')}
      />

      {/* Main Content */}
      <main className="overview-main">
        {/* Page Title */}
        <div className="page-header">
          <h1 className="page-title">
            Treasury Dashboard
            <img src={spanImage} alt="Decorative span" className="title-span" />
          </h1>
          <p className="page-subtitle">
            Live insights into how funds are held, ROI is distributed, and startups are supported.
            Empowering you to stake with trust.
          </p>
        </div>

        <div className="dashboard-layout">
          {/* Sidebar */}
          <aside className="dashboard-sidebar">
            <nav className="sidebar-nav">
              {sidebarItems.map((item) => (
                <button
                  key={item.id}
                  className={`sidebar-item ${activeTab === item.id ? 'active' : ''}`}
                  onClick={() => handleSidebarClick(item.id)}
                >
                  <img src={item.icon} alt={item.label} className="sidebar-icon" />
                  <span className="sidebar-label">{item.label}</span>
                </button>
              ))}
            </nav>
          </aside>

          {/* Main Dashboard Content */}
          <div className="dashboard-content">
            <div className="dashboard-cards">
              {dashboardCards.map((card, index) => (
                <div key={index} className="dashboard-card">
                  <div className="card-header">
                    <h3 className="card-title">
                      {card.title}
                      {card.titleIcon && <img src={card.titleIcon} alt="Title icon" className="title-icon" />}
                    </h3>
                  </div>
                  <div className="card-content">
                    <div className="card-value">
                      {card.value}
                      {card.unit && <span className="card-unit">{card.unit}</span>}
                    </div>
                    <div className={`card-change ${card.changeType}`}>
                      <img src={card.changeIcon} alt="Change indicator" className="change-icon" />
                      {card.change}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <Footer />
    </div>
  );
};

export default Overview;
