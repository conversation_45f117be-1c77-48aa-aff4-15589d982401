/* Overview Container */
.overview-container {
  min-height: 100vh;
  background: #1A1A1A;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  position: relative;
  overflow-x: hidden;
}

/* Background Blur Gradients */
.blur-gradient {
  position: absolute;
  border-radius: 50%;
  z-index: 1;
}

.blur-gradient-1 {
  width: 316px;
  height: 316px;
  left: -150px;
  top: -150px;
  background: #CC6754;
  opacity: 0.2;
  filter: blur(200px);
}

.blur-gradient-2 {
  width: 239px;
  height: 239px;
  left: -50px;
  top: -50px;
  background: #D19049;
  opacity: 0.15;
  filter: blur(150px);
}

/* Main Content */
.overview-main {
  padding-top: 120px;
  padding-bottom: 80px;
  min-height: calc(100vh - 120px);
  position: relative;
  z-index: 2;
}

/* Page Header */
.page-header {
  text-align: center;
  padding: 60px 40px 40px;
  max-width: 800px;
  margin: 0 auto;
}

.page-title {
  font-size: 48px;
  font-weight: 600;
  margin: 0 0 24px 0;
  color: #FFFFFF;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.title-span {
  max-width: 200px;
  height: auto;
}

.page-subtitle {
  font-size: 16px;
  color: #CCCCCC;
  line-height: 24px;
  margin: 0;
}

/* Dashboard Layout */
.dashboard-layout {
  display: flex;
  gap: 40px;
  padding: 0 40px;
  max-width: 1400px;
  margin: 0 auto;
}

/* Sidebar */
.dashboard-sidebar {
  width: 240px;
  background: rgba(38, 38, 38, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  height: fit-content;
  position: sticky;
  top: 140px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.sidebar-nav {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.sidebar-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: none;
  border: none;
  border-radius: 8px;
  color: #CCCCCC;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  width: 100%;
  position: relative;
}

.sidebar-item:hover {
  background: rgba(255, 255, 255, 0.05);
  color: #FFFFFF;
}

.sidebar-item.active {
  background: linear-gradient(135deg, #BF4129 0%, #D19049 100%);
  color: #FFFFFF;
  box-shadow: 0 4px 16px rgba(191, 65, 41, 0.3);
}

.sidebar-icon {
  width: 20px;
  height: 20px;
  object-fit: contain;
  filter: brightness(0.8);
  transition: filter 0.3s ease;
}

.sidebar-item:hover .sidebar-icon,
.sidebar-item.active .sidebar-icon {
  filter: brightness(1);
}

/* Dashboard Content */
.dashboard-content {
  flex: 1;
  max-width: 920px;
}

.dashboard-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  align-items: flex-start;
  align-content: flex-start;
}

/* Dashboard Cards */
.dashboard-card {
  background: rgba(38, 38, 38, 0.6);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 24px;
  width: calc(50% - 12px);
  min-height: 160px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.dashboard-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(191, 65, 41, 0.3);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: 12px;
  font-weight: 600;
  color: #999999;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin: 0;
  line-height: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-icon {
  width: 16px;
  height: 16px;
  object-fit: contain;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.card-value {
  font-size: 28px;
  font-weight: 700;
  color: #FFFFFF;
  display: flex;
  align-items: baseline;
  gap: 8px;
}

.card-unit {
  font-size: 16px;
  font-weight: 500;
  color: #CCCCCC;
}

.card-change {
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.change-icon {
  width: 16px;
  height: 16px;
  object-fit: contain;
}

.card-change.positive {
  color: #4CAF50;
}

.card-change.negative {
  color: #FF6B6B;
}



/* Mobile Responsive */
@media (max-width: 768px) {
  .blur-gradient-1 {
    width: 200px;
    height: 200px;
    left: -80px;
    top: 60px;
  }

  .blur-gradient-2 {
    width: 150px;
    height: 150px;
    right: -60px;
    top: 250px;
  }

  .overview-main {
    padding-top: 100px;
  }

  .page-header {
    padding: 40px 20px 30px;
  }

  .page-title {
    font-size: 32px;
    gap: 12px;
  }

  .title-span {
    max-width: 150px;
  }

  .page-subtitle {
    font-size: 14px;
  }

  .dashboard-layout {
    flex-direction: column;
    padding: 0 20px;
    gap: 24px;
  }

  .dashboard-sidebar {
    width: 100%;
    position: static;
    order: 1;
    top: auto;
    padding: 12px;
    margin-bottom: 24px;
    border-radius: 12px;
  }

  .sidebar-nav {
    flex-direction: row;
    overflow-x: auto;
    gap: 8px;
    padding-bottom: 8px;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .sidebar-nav::-webkit-scrollbar {
    display: none;
  }

  .sidebar-item {
    white-space: nowrap;
    min-width: fit-content;
    padding: 8px 12px;
    flex-shrink: 0;
    border-radius: 8px;
    font-size: 12px;
  }

  .sidebar-icon {
    width: 16px;
    height: 16px;
  }

  .dashboard-content {
    order: 2;
  }

  .dashboard-cards {
    gap: 16px;
  }

  .dashboard-card {
    width: 100%;
    padding: 20px;
    min-height: 140px;
  }

  .card-value {
    font-size: 24px;
  }
}
