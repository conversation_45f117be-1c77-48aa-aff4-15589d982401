import { BrowserRouter as Router, Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import { useEffect } from 'react';
import './styles/App.css';
import Header from './components/Header';
import Hero from './components/Hero';
import TreasuryDashboard from './components/TreasuryDashboard';
import ComparisonTable from './components/ComparisonTable';
import FAQ from './components/FAQ';
import Footer from './components/Footer';
import SignUp from './components/SignUp';
import ForgotPassword from './components/ForgotPassword';
import VerifyEmail from './components/VerifyEmail';
import Overview from './components/Overview';

// Scroll to top component
const ScrollToTop = () => {
  const location = useLocation();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [location]);

  return null;
};

// Landing Page Component
const LandingPage = () => {
  const navigate = useNavigate();

  return (
    <div className="landing-page">
      <Header
        onNavigateToSignUp={() => navigate('/signup')}
        onNavigateToLogin={() => navigate('/login')}
        onNavigateToLanding={() => navigate('/')}
      />
      <Hero />
      <TreasuryDashboard />
      <ComparisonTable />
      <FAQ />
      <Footer />
    </div>
  );
};

// Auth Page Component
const AuthPage = ({ children }: { children: React.ReactNode }) => {
  const navigate = useNavigate();

  return (
    <div className="auth-page">
      <Header
        onNavigateToSignUp={() => navigate('/signup')}
        onNavigateToLogin={() => navigate('/login')}
        onNavigateToLanding={() => navigate('/')}
      />
      <main className="auth-main">
        {children}
      </main>
      <Footer />
    </div>
  );
};

function App() {
  return (
    <Router>
      <ScrollToTop />
      <div className="app">
        <Routes>
          <Route path="/" element={<LandingPage />} />
          <Route path="/signup" element={
            <AuthPage>
              <SignUp initialMode="signup" />
            </AuthPage>
          } />
          <Route path="/login" element={
            <AuthPage>
              <SignUp initialMode="login" />
            </AuthPage>
          } />
          <Route path="/forgot-password" element={
            <AuthPage>
              <ForgotPassword onBack={() => window.history.back()} />
            </AuthPage>
          } />
          <Route path="/verify-email" element={
            <AuthPage>
              <VerifyEmail onBack={() => window.history.back()} />
            </AuthPage>
          } />
          <Route path="/overview" element={<Overview />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
